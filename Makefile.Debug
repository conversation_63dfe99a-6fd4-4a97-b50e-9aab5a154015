#############################################################################
# Makefile for building: hello
# Generated by qmake (3.0) (Qt 5.6.3)
# Project:  hello.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

####### Compiler, tools and options

CC            = cl
CXX           = cl
DEFINES       = -DUNICODE -DWIN32 -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -nologo -Zc:wchar_t -FS -Zi -MDd -W3 /Fddebug\hello.vc.pdb $(DEFINES)
CXXFLAGS      = -nologo -Zc:wchar_t -FS -Zi -MDd -GR -W3 -w34100 -w34189 -w44996 -EHsc /Fddebug\hello.vc.pdb $(DEFINES)
INCPATH       = -I. -IF:\QT\Qt5.6.3_32\5.6.3\msvc2013\include -IF:\QT\Qt5.6.3_32\5.6.3\msvc2013\include\QtGui -IF:\QT\Qt5.6.3_32\5.6.3\msvc2013\include\QtANGLE -IF:\QT\Qt5.6.3_32\5.6.3\msvc2013\include\QtCore -Idebug -IF:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\win32-msvc2013 
LINKER        = link
LFLAGS        = /NOLOGO /DYNAMICBASE /NXCOMPAT /DEBUG /SUBSYSTEM:CONSOLE "/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='*******' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"
LIBS          = /LIBPATH:F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Guid.lib F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Cored.lib 
QMAKE         = F:\QT\Qt5.6.3_32\5.6.3\msvc2013\bin\qmake.exe
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
COPY          = copy /y
SED           = $(QMAKE) -install sed
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
DEL_FILE      = del
DEL_DIR       = rmdir
MOVE          = move
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
INSTALL_FILE    = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR     = xcopy /s /q /y /i

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = hello.cpp 
OBJECTS       = debug\hello.obj

DIST          =   hello.cpp
QMAKE_TARGET  = hello
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = hello.exe
DESTDIR_TARGET = debug\hello.exe

####### Implicit rules

.SUFFIXES: .c .cpp .cc .cxx

{.}.cpp{debug\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fodebug\ @<<
	$<
<<

{.}.cc{debug\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fodebug\ @<<
	$<
<<

{.}.cxx{debug\}.obj::
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -Fodebug\ @<<
	$<
<<

{.}.c{debug\}.obj::
	$(CC) -c $(CFLAGS) $(INCPATH) -Fodebug\ @<<
	$<
<<

####### Build rules

first: all
all: Makefile.Debug  $(DESTDIR_TARGET)

$(DESTDIR_TARGET):  $(OBJECTS) 
	$(LINKER) $(LFLAGS) /MANIFEST:embed /OUT:$(DESTDIR_TARGET) @<<
$(OBJECTS) $(LIBS)
<<

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug hello.pro

qmake_all: FORCE

dist:
	$(ZIP) hello.zip $(SOURCES) $(DIST) hello.pro F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_pre.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\angle.conf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-base.conf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-desktop.conf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\qconfig.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bootstrap_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_clucene_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designercomponents_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_platformsupport_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmldevtools_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickparticles_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uiplugin.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_zlib_private.pri F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_functions.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_config.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\qt_config.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\win32-msvc2013\qmake.conf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_post.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_pre.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\default_pre.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resolve_config.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds_post.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_post.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\build_pass.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\console.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\rtti.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\precompile_header.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\warn_on.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resources.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\moc.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\opengl.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\file_copies.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\testcase_targets.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exceptions.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\yacc.prf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\lex.prf hello.pro F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Guid.prl F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Cored.prl     hello.cpp    

clean: compiler_clean 
	-$(DEL_FILE) debug\hello.obj
	-$(DEL_FILE) debug\hello.exp debug\hello.vc.pdb debug\hello.ilk debug\hello.idb

distclean: clean 
	-$(DEL_FILE) debug\hello.lib debug\hello.pdb
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_header_make_all:
compiler_moc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: 



####### Compile

debug\hello.obj: hello.cpp 

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

