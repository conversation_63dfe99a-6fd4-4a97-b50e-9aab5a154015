#############################################################################
# Makefile for building: hello
# Generated by qmake (3.0) (Qt 5.6.3)
# Project:  hello.pro
# Template: app
# Command: F:\QT\Qt5.6.3_32\5.6.3\msvc2013\bin\qmake.exe -o Makefile hello.pro
#############################################################################

MAKEFILE      = Makefile

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = F:\QT\Qt5.6.3_32\5.6.3\msvc2013\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
SUBTARGETS    =  \
		release \
		debug


release: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: hello.pro F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\win32-msvc2013\qmake.conf F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_pre.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\angle.conf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-base.conf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-desktop.conf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\qconfig.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bootstrap_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_clucene_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designercomponents_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_platformsupport_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmldevtools_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickparticles_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uiplugin.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_zlib_private.pri \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_functions.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_config.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\qt_config.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\win32-msvc2013\qmake.conf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_post.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_pre.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\default_pre.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resolve_config.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds_post.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_post.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\console.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\rtti.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\precompile_header.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\warn_on.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resources.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\moc.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\opengl.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\file_copies.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\testcase_targets.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exceptions.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\yacc.prf \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\lex.prf \
		hello.pro \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Gui.prl \
		F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Core.prl
	$(QMAKE) -o Makefile hello.pro
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_pre.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\angle.conf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-base.conf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\common\msvc-desktop.conf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\qconfig.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axbase_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axcontainer_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_axserver_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bluetooth_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_bootstrap_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_clucene_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_concurrent_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_core_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_dbus_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designer_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_designercomponents_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_gui_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_help_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimedia_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_multimediawidgets_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_network_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_nfc_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_opengl_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_openglextensions_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_platformsupport_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_positioning_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_printsupport_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qml_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmldevtools_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qmltest_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quick_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickparticles_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_quickwidgets_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sensors_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_serialport_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_sql_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_svg_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_testlib_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uiplugin.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_uitools_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_webchannel_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_websockets_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_widgets_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_winextras_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xml_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_xmlpatterns_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\modules\qt_lib_zlib_private.pri:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_functions.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt_config.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\qt_config.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\win32-msvc2013\qmake.conf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\spec_post.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_pre.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\default_pre.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resolve_config.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exclusive_builds_post.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\default_post.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\console.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\rtti.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\precompile_header.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\warn_on.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\qt.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\resources.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\moc.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\win32\opengl.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\file_copies.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\testcase_targets.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\exceptions.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\yacc.prf:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\mkspecs\features\lex.prf:
hello.pro:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Gui.prl:
F:\QT\Qt5.6.3_32\5.6.3\msvc2013\lib\Qt5Core.prl:
qmake: FORCE
	@$(QMAKE) -o Makefile hello.pro

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
	-$(DEL_FILE) hello.exp
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) hello.lib

release-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	@set MAKEFLAGS=$(MAKEFLAGS)
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile
